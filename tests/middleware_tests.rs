use ai_proxy::{
    middleware::{logging_middleware, error_handling_middleware, request_id_middleware, validation_middleware, performance_middleware},
    errors::AppError,
    server::AppState,
    config::{Config, ServerConfig, ProviderDetail, LoggingConfig, SecurityConfig, PerformanceConfig},
    providers::registry::ProviderRegistry,
    metrics::MetricsCollector,
};
use axum::{
    body::Body,
    http::{Request, StatusCode},
    response::Response,
    middleware::Next,
    extract::State,
};
use reqwest::Client;
use std::{collections::HashMap, sync::Arc};
use tokio::sync::Mutex;
use tower::ServiceExt;

// Helper function to create test app state
fn create_test_app_state() -> AppState {
    let mut providers = HashMap::new();
    providers.insert(
        "test_provider".to_string(),
        ProviderDetail {
            api_key: "test-api-key-**********".to_string(),
            api_base: "https://api.example.com/v1/".to_string(),
            models: Some(vec!["test-model".to_string()]),
            timeout_seconds: 30,
            max_retries: 3,
            enabled: true,
            rate_limit: None,
        },
    );

    let config = Config {
        server: ServerConfig {
            host: "127.0.0.1".to_string(),
            port: 3000,
            request_timeout_seconds: 30,
            max_request_size_bytes: 1024 * 1024,
        },
        providers,
        logging: LoggingConfig::default(),
        security: SecurityConfig::default(),
        performance: PerformanceConfig::default(),
    };

    let http_client = Client::new();
    let provider_registry = Arc::new(Mutex::new(ProviderRegistry::new()));
    let metrics = Arc::new(MetricsCollector::new());
    
    AppState {
        config: Arc::new(config),
        http_client,
        provider_registry,
        metrics,
    }
}

// Mock next handler for testing middleware
async fn mock_next_success(_req: Request<Body>) -> Response<Body> {
    Response::builder()
        .status(StatusCode::OK)
        .body(Body::from("Success"))
        .unwrap()
}

async fn mock_next_error(_req: Request<Body>) -> Response<Body> {
    Response::builder()
        .status(StatusCode::BAD_REQUEST)
        .body(Body::from("Error"))
        .unwrap()
}

#[tokio::test]
async fn test_request_id_middleware_adds_header() {
    let request = Request::builder()
        .uri("/test")
        .body(Body::empty())
        .unwrap();

    let response = request_id_middleware(request, Next::new(mock_next_success)).await;
    
    // Check that request ID header is added
    assert!(response.headers().contains_key("x-request-id"));
    let request_id = response.headers().get("x-request-id").unwrap();
    assert!(!request_id.is_empty());
    
    // Request ID should be a valid UUID format
    let request_id_str = request_id.to_str().unwrap();
    assert!(request_id_str.len() >= 32); // UUID without hyphens is 32 chars
}

#[tokio::test]
async fn test_request_id_middleware_preserves_existing_id() {
    let existing_id = "existing-request-id-123";
    let request = Request::builder()
        .uri("/test")
        .header("x-request-id", existing_id)
        .body(Body::empty())
        .unwrap();

    let response = request_id_middleware(request, Next::new(mock_next_success)).await;
    
    // Should preserve existing request ID (middleware doesn't overwrite existing IDs)
    let response_id = response.headers().get("x-request-id").unwrap().to_str().unwrap();
    assert_eq!(response_id, existing_id);
}

#[tokio::test]
async fn test_logging_middleware_logs_request_response() {
    let app_state = create_test_app_state();
    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .header("x-request-id", "test-request-123")
        .body(Body::from(r#"{"model": "claude-3-sonnet", "messages": []}"#))
        .unwrap();

    // This test mainly ensures the middleware doesn't panic
    // Actual log verification would require capturing log output
    let result = logging_middleware(State(app_state), request, Next::new(mock_next_success)).await;
    
    assert!(result.is_ok());
    let response = result.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_logging_middleware_logs_errors() {
    let app_state = create_test_app_state();
    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("x-request-id", "test-request-error")
        .body(Body::empty())
        .unwrap();

    let result = logging_middleware(State(app_state), request, Next::new(mock_next_error)).await;
    
    // Should handle the response (logging middleware doesn't change status)
    assert!(result.is_ok());
    let response = result.unwrap();
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}

#[tokio::test]
async fn test_error_handling_middleware_passes_through() {
    let request = Request::builder()
        .uri("/test")
        .body(Body::empty())
        .unwrap();

    let response = error_handling_middleware(request, Next::new(mock_next_error)).await;
    
    // Error handling middleware just logs, doesn't change the response
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}

#[tokio::test]
async fn test_error_handling_middleware_passes_success() {
    let request = Request::builder()
        .uri("/test")
        .body(Body::empty())
        .unwrap();

    let response = error_handling_middleware(request, Next::new(mock_next_success)).await;
    
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_validation_middleware_valid_post_request() {
    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(r#"{"model": "test-model", "messages": []}"#))
        .unwrap();

    let result = validation_middleware(request, Next::new(mock_next_success)).await;
    
    assert!(result.is_ok());
    let response = result.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_validation_middleware_missing_content_type() {
    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .body(Body::from(r#"{"model": "test-model"}"#))
        .unwrap();

    let result = validation_middleware(request, Next::new(mock_next_success)).await;
    
    assert!(result.is_err());
    let error = result.unwrap_err();
    assert!(error.to_string().contains("Content-Type header is required"));
}

#[tokio::test]
async fn test_validation_middleware_invalid_content_type() {
    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "text/plain")
        .body(Body::from("plain text"))
        .unwrap();

    let result = validation_middleware(request, Next::new(mock_next_success)).await;
    
    assert!(result.is_err());
    let error = result.unwrap_err();
    assert!(error.to_string().contains("Content-Type must be application/json"));
}

#[tokio::test]
async fn test_validation_middleware_get_request_passes() {
    let request = Request::builder()
        .method("GET")
        .uri("/health")
        .body(Body::empty())
        .unwrap();

    let result = validation_middleware(request, Next::new(mock_next_success)).await;
    
    assert!(result.is_ok());
    let response = result.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_performance_middleware() {
    let app_state = create_test_app_state();
    let request = Request::builder()
        .uri("/v1/messages")
        .header("x-request-id", "perf-test-123")
        .body(Body::empty())
        .unwrap();

    let result = performance_middleware(State(app_state), request, Next::new(mock_next_success)).await;
    
    assert!(result.is_ok());
    let response = result.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}